#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
余弦相似度结果排序工具 (Cosine Similarity Results Sorter)

此脚本用于对余弦相似度分析结果进行排序和分析。
可以读取余弦相似度结果JSON文件，按相似度从高到低排序，并生成详细的分析报告。

用法:
    python cosine_similarity_sorter.py input_file [--output_file OUTPUT_FILE] [--top_n TOP_N]

参数:
    input_file              输入的余弦相似度结果JSON文件路径
    --output_file, -o       输出的排序后JSON文件路径 (默认: cosine_similarity_sorted.json)
    --top_n, -n            显示前N个最相似的结果摘要 (默认: 10)

输入文件格式:
    JSON数组，每个元素包含：
    - similarity: 相似度值
    - response1: 第一个回复内容
    - response2: 第二个回复内容
    - source1: 第一个回复的来源ID
    - source2: 第二个回复的来源ID
    - is_same_conversation: 是否来自同一对话

输出:
    - 按相似度从高到低排序的JSON文件
    - 控制台输出前N个最相似结果的摘要
    - 统计信息（总数、同一对话占比等）
"""

import json
import argparse
import os
from collections import Counter


def load_cosine_similarity_results(input_file):
    """
    加载余弦相似度结果JSON文件
    
    参数:
        input_file: 输入文件路径
        
    返回:
        data: 加载的数据列表
    """
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        if not isinstance(data, list):
            raise ValueError("输入文件应包含一个JSON数组")
        
        print(f"成功加载 {len(data)} 条相似度结果")
        return data
        
    except FileNotFoundError:
        print(f"错误: 找不到输入文件 {input_file}")
        return []
    except json.JSONDecodeError as e:
        print(f"错误: JSON解析失败 - {e}")
        return []
    except Exception as e:
        print(f"错误: {e}")
        return []


def sort_by_similarity(data):
    """
    按相似度从高到低排序
    
    参数:
        data: 原始数据列表
        
    返回:
        sorted_data: 排序后的数据列表
    """
    # 按相似度从高到低排序
    sorted_data = sorted(data, key=lambda x: x.get('similarity', 0), reverse=True)
    
    # 添加排名信息
    for i, item in enumerate(sorted_data, 1):
        item['rank'] = i
    
    return sorted_data


def analyze_similarity_results(sorted_data):
    """
    分析相似度结果的统计信息
    
    参数:
        sorted_data: 排序后的数据列表
        
    返回:
        stats: 统计信息字典
    """
    if not sorted_data:
        return {}
    
    # 基本统计
    total_count = len(sorted_data)
    similarities = [item.get('similarity', 0) for item in sorted_data]
    
    # 相似度统计
    max_similarity = max(similarities)
    min_similarity = min(similarities)
    avg_similarity = sum(similarities) / len(similarities)
    
    # 同一对话统计
    same_conversation_count = sum(1 for item in sorted_data if item.get('is_same_conversation', False))
    different_conversation_count = total_count - same_conversation_count
    
    # 相似度区间统计
    high_similarity_count = sum(1 for s in similarities if s >= 0.9)
    medium_similarity_count = sum(1 for s in similarities if 0.8 <= s < 0.9)
    low_similarity_count = sum(1 for s in similarities if s < 0.8)
    
    # 来源统计
    all_sources = []
    for item in sorted_data:
        all_sources.extend([item.get('source1', ''), item.get('source2', '')])
    source_counter = Counter(all_sources)
    most_common_sources = source_counter.most_common(10)
    
    stats = {
        'total_count': total_count,
        'max_similarity': max_similarity,
        'min_similarity': min_similarity,
        'avg_similarity': avg_similarity,
        'same_conversation_count': same_conversation_count,
        'different_conversation_count': different_conversation_count,
        'same_conversation_percentage': (same_conversation_count / total_count * 100) if total_count > 0 else 0,
        'high_similarity_count': high_similarity_count,
        'medium_similarity_count': medium_similarity_count,
        'low_similarity_count': low_similarity_count,
        'most_common_sources': most_common_sources
    }
    
    return stats


def print_top_results(sorted_data, top_n=10):
    """
    打印前N个最相似的结果摘要
    
    参数:
        sorted_data: 排序后的数据列表
        top_n: 显示前N个结果
    """
    if not sorted_data:
        print("没有数据可显示")
        return
    
    print(f"\n相似度最高的前{min(top_n, len(sorted_data))}对回复:")
    print("=" * 80)
    
    for i, item in enumerate(sorted_data[:top_n], 1):
        similarity = item.get('similarity', 0)
        source1 = item.get('source1', 'N/A')
        source2 = item.get('source2', 'N/A')
        is_same_conv = item.get('is_same_conversation', False)
        same_conv_text = "同一对话" if is_same_conv else "不同对话"
        
        print(f"{i:2d}. 相似度: {similarity:.4f} | {same_conv_text} | {source1[:8]}...{source2[:8]}...")
        
        # 显示回复内容的前50个字符
        resp1 = item.get('response1', '')
        resp2 = item.get('response2', '')
        
        resp1_preview = resp1[:50] + "..." if len(resp1) > 50 else resp1
        resp2_preview = resp2[:50] + "..." if len(resp2) > 50 else resp2
        
        print(f"    回复1: {resp1_preview}")
        print(f"    回复2: {resp2_preview}")
        print()


def print_statistics(stats):
    """
    打印统计信息
    
    参数:
        stats: 统计信息字典
    """
    if not stats:
        print("没有统计信息可显示")
        return
    
    print("\n统计信息:")
    print("=" * 50)
    print(f"总结果数: {stats['total_count']}")
    print(f"最高相似度: {stats['max_similarity']:.4f}")
    print(f"最低相似度: {stats['min_similarity']:.4f}")
    print(f"平均相似度: {stats['avg_similarity']:.4f}")
    print()
    
    print("对话来源分布:")
    print(f"  同一对话: {stats['same_conversation_count']} ({stats['same_conversation_percentage']:.1f}%)")
    print(f"  不同对话: {stats['different_conversation_count']} ({100-stats['same_conversation_percentage']:.1f}%)")
    print()
    
    print("相似度区间分布:")
    print(f"  高相似度 (≥0.9): {stats['high_similarity_count']}")
    print(f"  中相似度 (0.8-0.9): {stats['medium_similarity_count']}")
    print(f"  低相似度 (<0.8): {stats['low_similarity_count']}")
    print()
    
    if stats['most_common_sources']:
        print("最常出现的来源ID (前10个):")
        for source, count in stats['most_common_sources']:
            if source:  # 排除空字符串
                print(f"  {source[:16]}...: {count} 次")


def save_sorted_results(sorted_data, output_file):
    """
    保存排序后的结果到JSON文件
    
    参数:
        sorted_data: 排序后的数据列表
        output_file: 输出文件路径
    """
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(sorted_data, f, ensure_ascii=False, indent=4)
        
        print(f"\n已将 {len(sorted_data)} 条排序结果保存到: {output_file}")
        
    except Exception as e:
        print(f"保存文件时出错: {e}")


def main():
    parser = argparse.ArgumentParser(description="余弦相似度结果排序工具")
    parser.add_argument("input_file", help="输入的余弦相似度结果JSON文件路径")
    parser.add_argument("--output_file", "-o", default="cosine_similarity_sorted.json", 
                       help="输出的排序后JSON文件路径 (默认: cosine_similarity_sorted.json)")
    parser.add_argument("--top_n", "-n", type=int, default=10,
                       help="显示前N个最相似的结果摘要 (默认: 10)")
    
    args = parser.parse_args()
    
    # 检查输入文件是否存在
    if not os.path.exists(args.input_file):
        print(f"错误: 输入文件 {args.input_file} 不存在")
        return
    
    print(f"正在处理文件: {args.input_file}")
    
    # 加载数据
    data = load_cosine_similarity_results(args.input_file)
    if not data:
        return
    
    # 排序
    print("正在按相似度排序...")
    sorted_data = sort_by_similarity(data)
    
    # 分析统计信息
    print("正在分析统计信息...")
    stats = analyze_similarity_results(sorted_data)
    
    # 保存结果
    save_sorted_results(sorted_data, args.output_file)
    
    # 显示结果
    print_top_results(sorted_data, args.top_n)
    print_statistics(stats)
    
    print(f"\n处理完成！")


if __name__ == "__main__":
    main()
