#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
脚本用于提取长度超过阈值的用户消息和助手消息
- 用户消息：长度超过100的消息
- 助手消息：长度超过400的消息
输出字段：persona_id, role, content, length
按照length从长到短排序
"""

import json
import csv
from typing import List, Dict, Any

def load_dialogues(file_path: str) -> List[Dict[str, Any]]:
    """加载对话数据"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def extract_long_messages(dialogues: List[Dict[str, Any]]) -> tuple:
    """
    提取长度超过阈值的消息
    返回: (long_user_messages, long_assistant_messages)
    """
    long_user_messages = []
    long_assistant_messages = []

    for dialogue in dialogues:
        persona_id = dialogue.get('persona_id', '')
        messages = dialogue.get('messages', [])

        for message in messages:
            role = message.get('role', '')
            content = message.get('content', '')
            length = len(content)

            message_data = {
                'persona_id': persona_id,
                'role': role,
                'content': content,
                'length': length
            }

            # 用户消息长度超过100
            if role == 'user' and length > 100:
                long_user_messages.append(message_data)

            # 助手消息长度超过400
            elif role == 'assistant' and length > 400:
                long_assistant_messages.append(message_data)

    # 按长度从长到短排序
    long_user_messages.sort(key=lambda x: x['length'], reverse=True)
    long_assistant_messages.sort(key=lambda x: x['length'], reverse=True)

    return long_user_messages, long_assistant_messages

def save_to_csv(messages: List[Dict[str, Any]], filename: str):
    """保存消息到CSV文件"""
    if not messages:
        print(f"没有找到符合条件的消息，跳过创建 {filename}")
        return

    fieldnames = ['persona_id', 'role', 'content', 'length']

    with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        writer.writerows(messages)

    print(f"已保存 {len(messages)} 条消息到 {filename}")

def main():
    # 输入文件路径
    input_file = 'processed_dialogues_openai.json'

    # 输出文件路径
    user_output_file = 'long_user_messages.csv'
    assistant_output_file = 'long_assistant_messages.csv'

    print("正在加载对话数据...")
    dialogues = load_dialogues(input_file)
    print(f"已加载 {len(dialogues)} 个对话")

    print("正在提取长消息...")
    long_user_messages, long_assistant_messages = extract_long_messages(dialogues)

    print(f"找到 {len(long_user_messages)} 条长度超过100的用户消息")
    print(f"找到 {len(long_assistant_messages)} 条长度超过400的助手消息")

    # 保存到CSV文件
    save_to_csv(long_user_messages, user_output_file)
    save_to_csv(long_assistant_messages, assistant_output_file)

    # 显示统计信息
    if long_user_messages:
        max_user_length = max(msg['length'] for msg in long_user_messages)
        min_user_length = min(msg['length'] for msg in long_user_messages)
        print(f"用户消息长度范围: {min_user_length} - {max_user_length}")

    if long_assistant_messages:
        max_assistant_length = max(msg['length'] for msg in long_assistant_messages)
        min_assistant_length = min(msg['length'] for msg in long_assistant_messages)
        print(f"助手消息长度范围: {min_assistant_length} - {max_assistant_length}")

    print("提取完成！")

if __name__ == "__main__":
    main()